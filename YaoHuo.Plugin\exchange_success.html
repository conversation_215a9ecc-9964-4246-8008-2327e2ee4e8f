<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兑换成功 - 妖晶兑换</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: #1f2937;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
            color: white;
            padding: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 480px;
            margin: 0 auto;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
        }

        .spacer {
            width: 40px;
        }

        /* Main Content */
        .main {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 80px);
            padding: 20px;
        }

        .success-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            padding: 32px;
            max-width: 400px;
            width: 100%;
            text-align: center;
            position: relative;
            overflow: hidden;
            opacity: 0;
            transform: translateY(20px) scale(0.95);
            animation: slideInUp 0.6s ease-out 0.2s forwards;
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Background decorations */
        .decoration {
            position: absolute;
            opacity: 0.05;
            pointer-events: none;
        }

        .decoration-1 {
            top: 16px;
            right: 16px;
            width: 32px;
            height: 32px;
        }

        .decoration-2 {
            bottom: 16px;
            left: 16px;
            width: 24px;
            height: 24px;
        }

        /* Success Icon */
        .success-icon-container {
            margin-bottom: 24px;
        }

        .success-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #5eead4 0%, #14b8a6 100%);
            border-radius: 50%;
            box-shadow: 0 10px 25px rgba(20, 184, 166, 0.3);
            margin-bottom: 16px;
            transform: scale(0);
            animation: bounceIn 0.8s ease-out 0.4s forwards;
        }

        @keyframes bounceIn {
            0% { transform: scale(0); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .checkmark {
            width: 40px;
            height: 40px;
            stroke: white;
            stroke-width: 3;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
        }

        .checkmark path {
            stroke-dasharray: 20;
            stroke-dashoffset: 20;
            animation: drawCheck 0.5s ease-out 0.8s forwards;
        }

        @keyframes drawCheck {
            to {
                stroke-dashoffset: 0;
            }
        }

        .success-title {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 24px;
            opacity: 0;
            animation: fadeInUp 0.5s ease-out 0.6s forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Exchange Info */
        .exchange-info {
            background: linear-gradient(135deg, #f0fdfa 0%, #ecfdf5 100%);
            border: 1px solid #a7f3d0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            opacity: 0;
            animation: fadeInUp 0.5s ease-out 0.8s forwards;
        }

        .exchange-label {
            font-size: 16px;
            color: #374151;
            margin-bottom: 8px;
        }

        .exchange-amount {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .amount-number {
            font-size: 32px;
            font-weight: 700;
            color: #14b8a6;
        }

        .amount-unit {
            font-size: 18px;
            color: #6b7280;
        }

        .sparkle {
            width: 20px;
            height: 20px;
            fill: #14b8a6;
        }

        /* Additional Info */
        .additional-info {
            background: #f9fafb;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            opacity: 0;
            animation: fadeInUp 0.5s ease-out 1s forwards;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            font-size: 14px;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .info-label {
            color: #6b7280;
        }

        .info-value {
            color: #1f2937;
            font-weight: 600;
        }

        /* Buttons */
        .button-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
            opacity: 0;
            animation: fadeInUp 0.5s ease-out 1.2s forwards;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(20, 184, 166, 0.4);
        }

        .btn-secondary {
            background: white;
            color: #14b8a6;
            border: 2px solid #a7f3d0;
        }

        .btn-secondary:hover {
            background: #f0fdfa;
            border-color: #14b8a6;
        }

        .arrow-left {
            width: 16px;
            height: 16px;
            stroke: currentColor;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
        }

        /* Confetti Animation */
        .confetti {
            position: absolute;
            width: 8px;
            height: 8px;
            background: linear-gradient(45deg, #14b8a6, #10b981);
            border-radius: 50%;
            pointer-events: none;
            opacity: 0;
        }

        .confetti.animate {
            animation: confettiFall 3s ease-out forwards;
        }

        @keyframes confettiFall {
            0% {
                opacity: 1;
                transform: translateY(-10px) rotate(0deg);
            }
            100% {
                opacity: 0;
                transform: translateY(400px) rotate(360deg);
            }
        }

        /* Responsive */
        @media (max-width: 480px) {
            .success-card {
                margin: 10px;
                padding: 24px;
            }
            
            .amount-number {
                font-size: 28px;
            }
            
            .success-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <button class="back-btn" onclick="goBack()">
                <svg class="arrow-left" viewBox="0 0 24 24">
                    <path d="m15 18-6-6 6-6"/>
                </svg>
            </button>
            <h1 class="header-title">充值与兑换</h1>
            <div class="spacer"></div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="success-card" id="successCard">

            <!-- Success Icon -->
            <div class="success-icon-container">
                <div class="success-icon">
                    <svg class="checkmark" viewBox="0 0 24 24">
                        <path d="M20 6L9 17l-5-5"/>
                    </svg>
                </div>
                <h2 class="success-title">兑换成功</h2>
            </div>

            <!-- Exchange Info -->
            <div class="exchange-info">
                <p class="exchange-label">成功兑换</p>
                <div class="exchange-amount">
                    <span class="amount-number">10,000</span>
                    <span class="amount-unit">妖晶</span>
                </div>
            </div>

            <!-- Additional Info -->
            <div class="additional-info">
                <div class="info-row">
                    <span class="info-label">兑换时间</span>
                    <span class="info-value" id="exchangeTime"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">当前余额</span>
                    <span class="info-value">15,000 妖晶</span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="button-container">
                <button class="btn btn-primary" onclick="goBack()">
                    <svg class="arrow-left" viewBox="0 0 24 24">
                        <path d="m15 18-6-6 6-6"/>
                    </svg>
                    返回
                </button>
                <button class="btn btn-secondary" onclick="continueExchange()">
                    继续兑换
                </button>
            </div>
        </div>
    </main>

    <script>
        // Set current time
        function setCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('exchangeTime').textContent = timeString;
        }

        // Create confetti effect
        function createConfetti() {
            const card = document.getElementById('successCard');
            const confettiCount = 20;

            for (let i = 0; i < confettiCount; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.className = 'confetti';
                    confetti.style.left = Math.random() * 100 + '%';
                    confetti.style.animationDelay = Math.random() * 0.5 + 's';
                    confetti.style.animationDuration = (2 + Math.random() * 2) + 's';
                    
                    card.appendChild(confetti);
                    
                    // Add animation class
                    setTimeout(() => {
                        confetti.classList.add('animate');
                    }, 10);
                    
                    // Remove confetti after animation
                    setTimeout(() => {
                        if (confetti.parentNode) {
                            confetti.parentNode.removeChild(confetti);
                        }
                    }, 3500);
                }, i * 100);
            }
        }

        // Button actions
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                alert('返回上一页');
            }
        }

        function continueExchange() {
            alert('继续兑换功能');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            setCurrentTime();
            
            // Start confetti after a delay
            setTimeout(createConfetti, 1000);
        });

        // Add some interactive effects
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>

