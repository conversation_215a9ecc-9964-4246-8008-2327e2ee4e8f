﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.ExUtility;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.BBS.Models;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.chinabank_WAP
{
    public class RMBtoMoney : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string KL_CheckIPTime = PubConstant.GetAppString("KL_CheckIPTime");

        public string action = "";

        public string tomoney = "";

        public string backurl = "";

        public string INFO = "";

        public string ERROR = "";

        public long STATE = 0L;

        public long changeMoney = 0L;

        public string changePW = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            // 基础初始化
            InitializePage();

            try
            {
                // 检查用户UI偏好并处理版本切换
                bool newVersionRendered = CheckAndHandleUIPreference();
                if (newVersionRendered)
                {
                    // 新版渲染成功，直接返回，不再执行后续的旧版代码
                    return;
                }
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }

            // 继续执行旧版逻辑...
            action = GetRequestValue("action");
            tomoney = GetRequestValue("tomoney");
            changePW = GetRequestValue("changePW");
            string siteDefault = WapTool.GetSiteDefault(siteVo.Version, 5);
            if (WapTool.IsNumeric(siteDefault))
            {
                STATE = long.Parse(siteDefault);
            }
            if (STATE < 1L)
            {
                INFO = "CLOSE";
            }
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            IsLogin(userid, backurl);
            string text = action;
            if (text != null && text == "add")
            {
                addMoney();
            }
        }

        public void addMoney()
        {
            if (STATE < 1L)
            {
                INFO = "CLOSE";
            }
            else if (PubConstant.md5(changePW).ToLower() != userVo.password.ToLower())
            {
                INFO = "PWERR";
            }
            else if (!WapTool.IsNumeric(tomoney) || tomoney.IndexOf('-') >= 0)
            {
                INFO = "NUM";
            }
            else if (isCheckIPTime(long.Parse(KL_CheckIPTime)))
            {
                INFO = "WAITING";
            }
            else if (userVo.RMB < 1m || userVo.RMB < (decimal)long.Parse(tomoney))
            {
                INFO = "NOTMONEY";
            }
            else if (long.Parse(tomoney) > 1000000L)
            {
                INFO = "MAXMONEY";
            }
            else
            {
                long num = long.Parse(tomoney) * STATE;
                string orderID = DateTime.Now.ToString("yyyyMMddhhmmss") + "-" + userid;

                // 使用参数化查询修复SQL注入问题
                string sql = "UPDATE [user] SET money = money + @addMoney, RMB = RMB - @deductRMB WHERE siteid = @siteid AND userid = @userid";
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@addMoney", SqlDbType.BigInt) { Value = num },
                    new SqlParameter("@deductRMB", SqlDbType.Decimal) { Value = decimal.Parse(tomoney) },
                    new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
                    new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) }
                };

                try
                {
                    // 执行安全的参数化查询
                    string connectionString = PubConstant.GetConnectionString(string_10);
                    int rowsAffected = DbHelperSQL.ExecuteNonQuery(connectionString, CommandType.Text, sql, parameters);

                    if (rowsAffected > 0)
                    {
                        // 记录日志
                        SaveRMBLog(userid, "-1", "-" + tomoney.ToString(), userid, nickname, "购买" + siteVo.sitemoneyname, orderID);
                        SaveBankLog(userid, "RMB转币", num.ToString(), userid, nickname, "花了￥" + tomoney + "购买");

                        INFO = "OK";
                    }
                    else
                    {
                        ERROR = "兑换失败：数据库更新失败";
                    }
                }
                catch (Exception ex)
                {
                    ERROR = "兑换失败: " + ex.Message;
                    System.Diagnostics.Debug.WriteLine($"RMBtoMoney: 数据库操作失败 - {ex.Message}");
                }
            }
            VisiteCount("购买了" + siteVo.sitemoneyname);
        }

        /// <summary>
        /// 基础页面初始化
        /// </summary>
        private void InitializePage()
        {
            // 这里可以添加一些基础的初始化逻辑
        }

        /// <summary>
        /// 初始化页面变量（新版UI使用）
        /// </summary>
        private void InitializePageVariables()
        {
            // 获取表单数据
            action = GetRequestValue("action");
            tomoney = GetRequestValue("tomoney");
            changePW = GetRequestValue("changePW");

            // 获取兑换比例设置
            string siteDefault = WapTool.GetSiteDefault(siteVo.Version, 5);
            if (WapTool.IsNumeric(siteDefault))
            {
                STATE = long.Parse(siteDefault);
            }

            // 检查功能是否关闭
            if (STATE < 1L)
            {
                INFO = "CLOSE";
            }

            // 设置返回URL
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);

            // 验证用户登录
            IsLogin(userid, backurl);
        }

        /// <summary>
        /// 检查用户UI偏好并处理版本切换
        /// </summary>
        private bool CheckAndHandleUIPreference()
        {
            string uiPreference = "";
            if (Request.Cookies["ui_preference"] != null)
            {
                uiPreference = Request.Cookies["ui_preference"].Value;
            }
            if (string.IsNullOrEmpty(uiPreference))
            {
                uiPreference = "old";
            }

            if (uiPreference == "new")
            {
                return TryRenderWithHandlebars();
            }
            return false;
        }

        /// <summary>
        /// 尝试使用Handlebars模板渲染页面
        /// </summary>
        private bool TryRenderWithHandlebars()
        {
            try
            {
                // 直接检查TemplateService可用性
                string viewMode = TemplateService.GetViewMode();
                if (viewMode == "new")
                {
                    RenderWithHandlebars();
                    return true;
                }
                return false;
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException 是正常的成功渲染标志
                System.Diagnostics.Debug.WriteLine("新版渲染成功，线程正常终止");
                return true;
            }
            catch (Exception ex)
            {
                ERROR = "新版模板加载失败: " + WapTool.ErrorToString(ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// 使用Handlebars模板渲染页面
        /// </summary>
        private void RenderWithHandlebars()
        {
            try
            {
                // 初始化必要的变量（与旧版逻辑保持一致）
                InitializePageVariables();

                // 处理表单提交（如果有）
                string action = base.Request.Form.Get("action");
                if (action == "add")
                {
                    ProcessExchangeRequest();
                }

                // 构建页面数据模型
                var pageModel = BuildRMBtoMoneyPageModel();

                // 调用新的 RenderPageWithLayout 方法
                string finalHtml = TemplateService.RenderPageWithLayout(
                    "~/Template/Pages/RMBtoMoney.hbs",
                    pageModel,
                    "充值与兑换",
                    new HeaderOptionsModel { ShowViewModeToggle = false }
                );

                // 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End();
            }
            catch (System.Threading.ThreadAbortException)
            {
                // Response.End() 的正常行为，直接重新抛出
                throw;
            }
            catch (Exception ex)
            {
                // 错误处理
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}</div>");
                HttpContext.Current.ApplicationInstance.CompleteRequest();
            }
        }

        /// <summary>
        /// 处理兑换请求（新版UI）
        /// </summary>
        private void ProcessExchangeRequest()
        {
            // 重新获取表单数据
            action = GetRequestValue("action");
            tomoney = GetRequestValue("tomoney");
            changePW = GetRequestValue("changePW");

            // 调用原有的兑换逻辑
            addMoney();
        }

        /// <summary>
        /// 构建页面数据模型
        /// </summary>
        private RMBtoMoneyPageModel BuildRMBtoMoneyPageModel()
        {
            var model = new RMBtoMoneyPageModel();

            // 构建消息状态
            BuildMessageModel(model);

            // 构建用户资产信息
            BuildUserAssetsModel(model);

            // 构建兑换设置
            BuildExchangeSettingsModel(model);

            // 构建充值信息
            BuildRechargeInfoModel(model);

            // 构建表单数据
            BuildFormDataModel(model);

            return model;
        }

        /// <summary>
        /// 构建消息模型
        /// </summary>
        private void BuildMessageModel(RMBtoMoneyPageModel model)
        {
            if (!string.IsNullOrEmpty(ERROR))
            {
                model.Message.SetError(ERROR);
            }
            else if (INFO == "OK")
            {
                model.Message.SetSuccess("兑换成功！");
            }
            else if (INFO == "CLOSE")
            {
                model.Message.SetError("站长已关闭此功能！");
            }
            else if (INFO == "PWERR")
            {
                model.Message.SetError("密码错误！");
            }
            else if (INFO == "NUM")
            {
                model.Message.SetError("金额需要数字！");
            }
            else if (INFO == "NOTMONEY")
            {
                model.Message.SetError($"你的RMB只有{userVo.RMB:F2}！");
            }
            else if (INFO == "MAXMONEY")
            {
                model.Message.SetError("每次不能大于￥1000.00！");
            }
            else if (INFO == "WAITING")
            {
                model.Message.SetWarning("操作过于频繁，请稍后再试！");
            }
        }

        /// <summary>
        /// 构建用户资产模型
        /// </summary>
        private void BuildUserAssetsModel(RMBtoMoneyPageModel model)
        {
            model.UserAssets.CurrentRMB = userVo.RMB;
            model.UserAssets.CurrentMoney = userVo.money;
            model.UserAssets.MoneyName = siteVo.sitemoneyname;
        }

        /// <summary>
        /// 构建兑换设置模型
        /// </summary>
        private void BuildExchangeSettingsModel(RMBtoMoneyPageModel model)
        {
            model.ExchangeSettings.ExchangeRate = STATE;
            model.ExchangeSettings.IsEnabled = STATE >= 1L;
            model.ExchangeSettings.MoneyName = siteVo.sitemoneyname;
            model.ExchangeSettings.MaxExchangeAmount = 1000.00m;
        }

        /// <summary>
        /// 构建充值信息模型
        /// </summary>
        private void BuildRechargeInfoModel(RMBtoMoneyPageModel model)
        {
            model.RechargeInfo.UserId = userid.ToString();
            model.RechargeInfo.SupportsAlipay = true;
            model.RechargeInfo.SupportsWechat = true;
        }

        /// <summary>
        /// 构建表单数据模型
        /// </summary>
        private void BuildFormDataModel(RMBtoMoneyPageModel model)
        {
            model.FormData.ActionUrl = http_start + "bbs/RMBtoMoney.aspx";
            model.FormData.ExchangeAmount = tomoney;
            model.FormData.HiddenFields.Action = "add";
            model.FormData.HiddenFields.SiteId = siteid.ToString();
            model.FormData.HiddenFields.BackUrl = backurl;
        }
    }
}