using System;

namespace YaoHuo.Plugin.BBS.Models
{
    /// <summary>
    /// RMBtoMoney 页面数据模型
    /// </summary>
    public class RMBtoMoneyPageModel
    {
        /// <summary>
        /// 页面标题
        /// </summary>
        public string PageTitle { get; set; } = "充值与兑换";

        /// <summary>
        /// 消息状态
        /// </summary>
        public MessageModel Message { get; set; } = new MessageModel();

        /// <summary>
        /// 用户资产信息
        /// </summary>
        public UserAssetsModel UserAssets { get; set; } = new UserAssetsModel();

        /// <summary>
        /// 兑换设置
        /// </summary>
        public ExchangeSettingsModel ExchangeSettings { get; set; } = new ExchangeSettingsModel();

        /// <summary>
        /// 充值信息
        /// </summary>
        public RechargeInfoModel RechargeInfo { get; set; } = new RechargeInfoModel();

        /// <summary>
        /// 表单数据
        /// </summary>
        public FormDataModel FormData { get; set; } = new FormDataModel();
    }

    /// <summary>
    /// 消息模型
    /// </summary>
    public class MessageModel
    {
        /// <summary>
        /// 是否有消息
        /// </summary>
        public bool HasMessage { get; set; }

        /// <summary>
        /// 消息类型 (success, error, warning, info)
        /// </summary>
        public string Type { get; set; } = "info";

        /// <summary>
        /// 消息内容
        /// </summary>
        public string Content { get; set; } = "";

        /// <summary>
        /// 是否为成功消息
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 是否为错误消息
        /// </summary>
        public bool IsError { get; set; }

        /// <summary>
        /// 设置成功消息
        /// </summary>
        public void SetSuccess(string message)
        {
            HasMessage = true;
            IsSuccess = true;
            IsError = false;
            Type = "success";
            Content = message;
        }

        /// <summary>
        /// 设置错误消息
        /// </summary>
        public void SetError(string message)
        {
            HasMessage = true;
            IsSuccess = false;
            IsError = true;
            Type = "error";
            Content = message;
        }

        /// <summary>
        /// 设置警告消息
        /// </summary>
        public void SetWarning(string message)
        {
            HasMessage = true;
            IsSuccess = false;
            IsError = false;
            Type = "warning";
            Content = message;
        }
    }

    /// <summary>
    /// 用户资产模型
    /// </summary>
    public class UserAssetsModel
    {
        /// <summary>
        /// 当前RMB余额
        /// </summary>
        public decimal CurrentRMB { get; set; }

        /// <summary>
        /// 当前妖晶余额
        /// </summary>
        public long CurrentMoney { get; set; }

        /// <summary>
        /// 妖晶名称
        /// </summary>
        public string MoneyName { get; set; } = "妖晶";

        /// <summary>
        /// RMB显示格式
        /// </summary>
        public string RMBDisplay => $"¥{CurrentRMB:F2}";

        /// <summary>
        /// 妖晶显示格式
        /// </summary>
        public string MoneyDisplay => CurrentMoney.ToString("N0");
    }

    /// <summary>
    /// 兑换设置模型
    /// </summary>
    public class ExchangeSettingsModel
    {
        /// <summary>
        /// 兑换比例 (1元 = ExchangeRate 妖晶)
        /// </summary>
        public long ExchangeRate { get; set; }

        /// <summary>
        /// 最大兑换金额
        /// </summary>
        public decimal MaxExchangeAmount { get; set; } = 1000.00m;

        /// <summary>
        /// 是否启用兑换功能
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 兑换比例显示
        /// </summary>
        public string RateDisplay => $"¥1 元 = {ExchangeRate} {MoneyName}";

        /// <summary>
        /// 妖晶名称
        /// </summary>
        public string MoneyName { get; set; } = "妖晶";

        /// <summary>
        /// 最大金额显示
        /// </summary>
        public string MaxAmountDisplay => $"¥{MaxExchangeAmount:F2}";
    }

    /// <summary>
    /// 充值信息模型
    /// </summary>
    public class RechargeInfoModel
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = "";

        /// <summary>
        /// 是否显示二维码
        /// </summary>
        public bool ShowQRCode { get; set; } = false;

        /// <summary>
        /// 支付方式 (alipay, wechat)
        /// </summary>
        public string PaymentMethod { get; set; } = "alipay";

        /// <summary>
        /// 是否支持支付宝
        /// </summary>
        public bool SupportsAlipay { get; set; } = true;

        /// <summary>
        /// 是否支持微信支付
        /// </summary>
        public bool SupportsWechat { get; set; } = true;

        /// <summary>
        /// 充值说明
        /// </summary>
        public string Instructions { get; set; } = "付款成功后请「通知站长」收款，24小时内会将相应的款值打到你的RMB账户上。";
    }

    /// <summary>
    /// 表单数据模型
    /// </summary>
    public class FormDataModel
    {
        /// <summary>
        /// 表单提交地址
        /// </summary>
        public string ActionUrl { get; set; } = "";

        /// <summary>
        /// 当前输入的兑换金额
        /// </summary>
        public string ExchangeAmount { get; set; } = "";

        /// <summary>
        /// 隐藏字段
        /// </summary>
        public HiddenFieldsModel HiddenFields { get; set; } = new HiddenFieldsModel();
    }

    /// <summary>
    /// 隐藏字段模型
    /// </summary>
    public class HiddenFieldsModel
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public string Action { get; set; } = "add";

        /// <summary>
        /// 站点ID
        /// </summary>
        public string SiteId { get; set; } = "";

        /// <summary>
        /// 返回URL
        /// </summary>
        public string BackUrl { get; set; } = "";
    }
}
